
import secrets

from stem import Signal
from stem.control import Controller
from tenacity import retry

@retry
def switch_proxy():
    # secrets.choice([time.sleep(0.5),time.sleep(0.9)])
    with Controller.from_port() as controller:
        controller.authenticate()
        controller.signal(Signal.NEWNYM)
        # time.sleep(controller.get_newnym_wait())
        # time.sleep(1.25)

@retry
def switch_tor():
    # random.choice([time.sleep(0.5),time.sleep(0.9)])
    with Controller.from_port(port = 9051) as controller:
        controller.authenticate()
        controller.signal(Signal.NEWNYM)
        # time.sleep(controller.get_newnym_wait())
        # time.sleep(1.25)

secrets.choice([switch_proxy(),switch_tor()])